"""Tests for certification utility functions"""
import pytest
from unittest.mock import patch, MagicMock
from utils.certification_utils import (
    calculate_study_time,
    get_certification_difficulty,
    find_related_certifications,
    validate_certification_data,
    get_certification_path,
    estimate_cost,
    check_prerequisites
)


class TestCalculateStudyTime:
    """Test study time calculation"""
    
    def test_calculate_basic_study_time(self):
        """Test basic study time calculation"""
        # Mock certification data
        cert_data = {
            'difficulty': 3,
            'estimated_hours': 100,
            'level': 'Intermediate'
        }
        
        study_time = calculate_study_time(cert_data, experience_level='Beginner')
        assert isinstance(study_time, int)
        assert study_time > 0
    
    def test_calculate_study_time_with_experience(self):
        """Test study time calculation with different experience levels"""
        cert_data = {
            'difficulty': 3,
            'estimated_hours': 100,
            'level': 'Advanced'
        }
        
        beginner_time = calculate_study_time(cert_data, experience_level='Beginner')
        expert_time = calculate_study_time(cert_data, experience_level='Expert')
        
        # Beginners should need more time than experts
        assert beginner_time > expert_time
    
    def test_calculate_study_time_invalid_data(self):
        """Test study time calculation with invalid data"""
        with pytest.raises(ValueError):
            calculate_study_time({}, experience_level='Beginner')
    
    def test_calculate_study_time_with_custom_hours(self):
        """Test study time calculation with custom hours"""
        cert_data = {
            'difficulty': 2,
            'estimated_hours': 80,
            'level': 'Intermediate',
            'custom_hours': 120
        }
        
        study_time = calculate_study_time(cert_data, experience_level='Intermediate')
        # Should use custom hours when available
        assert study_time >= 120


class TestGetCertificationDifficulty:
    """Test certification difficulty assessment"""
    
    def test_get_difficulty_by_level(self):
        """Test difficulty assessment by certification level"""
        assert get_certification_difficulty('Entry Level') == 1
        assert get_certification_difficulty('Beginner') == 1
        assert get_certification_difficulty('Intermediate') == 2
        assert get_certification_difficulty('Advanced') == 3
        assert get_certification_difficulty('Expert') == 4
    
    def test_get_difficulty_unknown_level(self):
        """Test difficulty assessment for unknown level"""
        difficulty = get_certification_difficulty('Unknown Level')
        assert difficulty == 2  # Should default to intermediate
    
    def test_get_difficulty_case_insensitive(self):
        """Test that difficulty assessment is case insensitive"""
        assert get_certification_difficulty('ADVANCED') == 3
        assert get_certification_difficulty('advanced') == 3
        assert get_certification_difficulty('Advanced') == 3


class TestFindRelatedCertifications:
    """Test finding related certifications"""
    
    @patch('utils.certification_utils.get_db')
    def test_find_related_by_domain(self, mock_get_db):
        """Test finding certifications by domain"""
        # Mock database session
        mock_session = MagicMock()
        mock_get_db.return_value.__enter__.return_value = mock_session
        
        # Mock query results
        mock_certs = [
            MagicMock(name='CISSP', domain='Security Management'),
            MagicMock(name='CISM', domain='Security Management'),
            MagicMock(name='CISA', domain='Security Management')
        ]
        mock_session.query.return_value.filter.return_value.all.return_value = mock_certs
        
        related = find_related_certifications('CISSP', by_domain=True)
        assert len(related) >= 0  # Should return list
    
    @patch('utils.certification_utils.get_db')
    def test_find_related_by_level(self, mock_get_db):
        """Test finding certifications by level"""
        mock_session = MagicMock()
        mock_get_db.return_value.__enter__.return_value = mock_session
        
        mock_certs = [
            MagicMock(name='Security+', level='Beginner'),
            MagicMock(name='CySA+', level='Beginner')
        ]
        mock_session.query.return_value.filter.return_value.all.return_value = mock_certs
        
        related = find_related_certifications('Security+', by_level=True)
        assert isinstance(related, list)
    
    def test_find_related_invalid_cert(self):
        """Test finding related certifications for invalid cert"""
        related = find_related_certifications('INVALID_CERT')
        assert related == []


class TestValidateCertificationData:
    """Test certification data validation"""
    
    def test_validate_complete_data(self):
        """Test validation of complete certification data"""
        cert_data = {
            'name': 'CISSP',
            'domain': 'Security Management',
            'level': 'Advanced',
            'cost': 749,
            'description': 'Information Systems Security Professional',
            'url': 'https://isc2.org/cissp'
        }
        
        is_valid, errors = validate_certification_data(cert_data)
        assert is_valid is True
        assert len(errors) == 0
    
    def test_validate_missing_required_fields(self):
        """Test validation with missing required fields"""
        cert_data = {
            'name': 'CISSP'
            # Missing other required fields
        }
        
        is_valid, errors = validate_certification_data(cert_data)
        assert is_valid is False
        assert len(errors) > 0
        assert any('domain' in error for error in errors)
    
    def test_validate_invalid_cost(self):
        """Test validation with invalid cost"""
        cert_data = {
            'name': 'CISSP',
            'domain': 'Security Management',
            'level': 'Advanced',
            'cost': -100  # Invalid negative cost
        }
        
        is_valid, errors = validate_certification_data(cert_data)
        assert is_valid is False
        assert any('cost' in error for error in errors)
    
    def test_validate_invalid_url(self):
        """Test validation with invalid URL"""
        cert_data = {
            'name': 'CISSP',
            'domain': 'Security Management',
            'level': 'Advanced',
            'url': 'not-a-valid-url'
        }
        
        is_valid, errors = validate_certification_data(cert_data)
        assert is_valid is False
        assert any('url' in error for error in errors)


class TestGetCertificationPath:
    """Test certification path generation"""
    
    @patch('utils.certification_utils.find_related_certifications')
    def test_get_path_for_beginner(self, mock_find_related):
        """Test getting certification path for beginner"""
        mock_find_related.return_value = [
            {'name': 'Security+', 'level': 'Beginner', 'difficulty': 1},
            {'name': 'CySA+', 'level': 'Intermediate', 'difficulty': 2},
            {'name': 'CISSP', 'level': 'Advanced', 'difficulty': 3}
        ]
        
        path = get_certification_path('Security Operations', experience_level='Beginner')
        assert isinstance(path, list)
        assert len(path) > 0
        
        # Path should be ordered by difficulty
        difficulties = [cert['difficulty'] for cert in path]
        assert difficulties == sorted(difficulties)
    
    @patch('utils.certification_utils.find_related_certifications')
    def test_get_path_for_expert(self, mock_find_related):
        """Test getting certification path for expert"""
        mock_find_related.return_value = [
            {'name': 'CISSP', 'level': 'Advanced', 'difficulty': 3},
            {'name': 'CISSP-ISSAP', 'level': 'Expert', 'difficulty': 4}
        ]
        
        path = get_certification_path('Security Management', experience_level='Expert')
        assert isinstance(path, list)
        # Experts should get more advanced certifications
        if path:
            assert all(cert['difficulty'] >= 2 for cert in path)


class TestEstimateCost:
    """Test cost estimation"""
    
    def test_estimate_single_cert_cost(self):
        """Test cost estimation for single certification"""
        cert_data = {
            'cost': 500,
            'training_cost': 1000,
            'materials_cost': 200
        }
        
        total_cost = estimate_cost([cert_data])
        assert total_cost == 1700  # Sum of all costs
    
    def test_estimate_multiple_certs_cost(self):
        """Test cost estimation for multiple certifications"""
        certs_data = [
            {'cost': 300, 'training_cost': 500},
            {'cost': 400, 'training_cost': 600},
            {'cost': 500, 'training_cost': 700}
        ]
        
        total_cost = estimate_cost(certs_data)
        assert total_cost == 3000  # Sum of all certification costs
    
    def test_estimate_cost_with_discount(self):
        """Test cost estimation with bulk discount"""
        certs_data = [
            {'cost': 500},
            {'cost': 500},
            {'cost': 500}
        ]
        
        total_cost = estimate_cost(certs_data, bulk_discount=0.1)
        assert total_cost == 1350  # 1500 * 0.9
    
    def test_estimate_cost_missing_data(self):
        """Test cost estimation with missing cost data"""
        cert_data = [{'name': 'Test Cert'}]  # No cost information
        
        total_cost = estimate_cost(cert_data)
        assert total_cost == 0


class TestCheckPrerequisites:
    """Test prerequisite checking"""
    
    def test_check_prerequisites_met(self):
        """Test checking when prerequisites are met"""
        target_cert = {
            'name': 'CISSP',
            'prerequisites': ['Security+', '5 years experience']
        }
        
        user_certs = ['Security+', 'Network+']
        user_experience = 6
        
        met, missing = check_prerequisites(target_cert, user_certs, user_experience)
        assert met is True
        assert len(missing) == 0
    
    def test_check_prerequisites_not_met(self):
        """Test checking when prerequisites are not met"""
        target_cert = {
            'name': 'CISSP',
            'prerequisites': ['Security+', 'CCNA', '5 years experience']
        }
        
        user_certs = ['Security+']
        user_experience = 2
        
        met, missing = check_prerequisites(target_cert, user_certs, user_experience)
        assert met is False
        assert len(missing) > 0
        assert 'CCNA' in str(missing)
    
    def test_check_prerequisites_no_requirements(self):
        """Test checking certification with no prerequisites"""
        target_cert = {
            'name': 'Security+',
            'prerequisites': []
        }
        
        met, missing = check_prerequisites(target_cert, [], 0)
        assert met is True
        assert len(missing) == 0
    
    def test_check_prerequisites_invalid_cert(self):
        """Test checking prerequisites for invalid certification"""
        with pytest.raises(ValueError):
            check_prerequisites({}, [], 0)


@pytest.fixture
def sample_certification():
    """Fixture providing sample certification data"""
    return {
        'name': 'CISSP',
        'domain': 'Security Management',
        'level': 'Advanced',
        'difficulty': 3,
        'cost': 749,
        'estimated_hours': 150,
        'prerequisites': ['5 years experience'],
        'description': 'Certified Information Systems Security Professional',
        'url': 'https://isc2.org/cissp'
    }


def test_integration_certification_analysis(sample_certification):
    """Test integration of multiple certification utility functions"""
    # Test that all functions work together
    difficulty = get_certification_difficulty(sample_certification['level'])
    assert difficulty == sample_certification['difficulty']
    
    study_time = calculate_study_time(sample_certification, experience_level='Intermediate')
    assert study_time > 0
    
    is_valid, errors = validate_certification_data(sample_certification)
    assert is_valid is True
    
    total_cost = estimate_cost([sample_certification])
    assert total_cost == sample_certification['cost']
