"""Tests for study resources utility functions"""
import pytest
from unittest.mock import patch, MagicMock
import requests
from utils.study_resources import (
    validate_resource_url,
    extract_study_materials,
    get_certification_resources,
    analyze_resource_quality,
    ResourceType,
    StudyResource
)


class TestValidateResourceUrl:
    """Test URL validation functionality"""
    
    @patch('utils.study_resources.requests.head')
    def test_validate_valid_url(self, mock_head):
        """Test validation of a valid URL"""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_head.return_value = mock_response
        
        result = validate_resource_url("https://example.com")
        assert result is True
        mock_head.assert_called_once_with("https://example.com", timeout=5, allow_redirects=True)
    
    @patch('utils.study_resources.requests.head')
    def test_validate_invalid_url(self, mock_head):
        """Test validation of an invalid URL"""
        mock_response = MagicMock()
        mock_response.status_code = 404
        mock_head.return_value = mock_response
        
        result = validate_resource_url("https://invalid.com")
        assert result is False
    
    @patch('utils.study_resources.requests.head')
    def test_validate_url_timeout(self, mock_head):
        """Test URL validation with timeout"""
        mock_head.side_effect = requests.Timeout()
        
        result = validate_resource_url("https://slow.com")
        assert result is False
    
    @patch('utils.study_resources.requests.head')
    def test_validate_url_connection_error(self, mock_head):
        """Test URL validation with connection error"""
        mock_head.side_effect = requests.ConnectionError()
        
        result = validate_resource_url("https://unreachable.com")
        assert result is False


class TestExtractStudyMaterials:
    """Test study materials extraction"""
    
    @patch('utils.study_resources.trafilatura.extract')
    @patch('utils.study_resources.requests.get')
    def test_extract_from_valid_page(self, mock_get, mock_extract):
        """Test extracting materials from a valid page"""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.content = b"<html><body>Study content</body></html>"
        mock_get.return_value = mock_response
        
        mock_extract.return_value = "Extracted study content"
        
        result = extract_study_materials("https://example.com/study")
        assert result == "Extracted study content"
    
    @patch('utils.study_resources.requests.get')
    def test_extract_from_invalid_page(self, mock_get):
        """Test extracting materials from invalid page"""
        mock_response = MagicMock()
        mock_response.status_code = 404
        mock_get.return_value = mock_response
        
        result = extract_study_materials("https://example.com/404")
        assert result is None
    
    @patch('utils.study_resources.requests.get')
    def test_extract_with_request_exception(self, mock_get):
        """Test extraction with request exception"""
        mock_get.side_effect = requests.RequestException("Network error")
        
        result = extract_study_materials("https://example.com/error")
        assert result is None


class TestGetCertificationResources:
    """Test certification resource gathering"""
    
    @patch('utils.study_resources.validate_resource_url')
    def test_get_resources_for_known_cert(self, mock_validate):
        """Test getting resources for a known certification"""
        mock_validate.return_value = True
        
        resources = get_certification_resources("CISSP")
        assert isinstance(resources, list)
        assert len(resources) > 0
        
        # Check that all resources have required fields
        for resource in resources:
            assert hasattr(resource, 'title')
            assert hasattr(resource, 'url')
            assert hasattr(resource, 'type')
    
    def test_get_resources_for_unknown_cert(self):
        """Test getting resources for unknown certification"""
        resources = get_certification_resources("UNKNOWN_CERT")
        assert isinstance(resources, list)
        # Should return empty list or default resources
    
    @patch('utils.study_resources.validate_resource_url')
    def test_get_resources_filters_invalid_urls(self, mock_validate):
        """Test that invalid URLs are filtered out"""
        # Mock some URLs as invalid
        mock_validate.side_effect = lambda url: "valid" in url
        
        resources = get_certification_resources("CISSP")
        # All returned resources should have valid URLs
        for resource in resources:
            assert "valid" in resource.url or mock_validate.return_value


class TestAnalyzeResourceQuality:
    """Test resource quality analysis"""
    
    def test_analyze_high_quality_resource(self):
        """Test analysis of high-quality resource"""
        resource = StudyResource(
            title="Comprehensive CISSP Study Guide",
            url="https://official-site.com/cissp-guide",
            type=ResourceType.OFFICIAL_GUIDE,
            description="Official study guide with 500+ pages",
            rating=4.8,
            review_count=1000
        )
        
        quality_score = analyze_resource_quality(resource)
        assert quality_score > 0.8  # High quality score
    
    def test_analyze_low_quality_resource(self):
        """Test analysis of low-quality resource"""
        resource = StudyResource(
            title="Quick CISSP Tips",
            url="https://random-blog.com/cissp",
            type=ResourceType.BLOG_POST,
            description="Short blog post",
            rating=2.1,
            review_count=5
        )
        
        quality_score = analyze_resource_quality(resource)
        assert quality_score < 0.5  # Low quality score
    
    def test_analyze_resource_without_rating(self):
        """Test analysis of resource without rating"""
        resource = StudyResource(
            title="CISSP Study Notes",
            url="https://example.com/notes",
            type=ResourceType.STUDY_NOTES,
            description="Personal study notes"
        )
        
        quality_score = analyze_resource_quality(resource)
        assert 0 <= quality_score <= 1  # Valid score range


class TestResourceType:
    """Test ResourceType enum"""
    
    def test_resource_type_values(self):
        """Test that ResourceType has expected values"""
        assert hasattr(ResourceType, 'OFFICIAL_GUIDE')
        assert hasattr(ResourceType, 'VIDEO_COURSE')
        assert hasattr(ResourceType, 'PRACTICE_EXAM')
        assert hasattr(ResourceType, 'BLOG_POST')
        assert hasattr(ResourceType, 'STUDY_NOTES')


class TestStudyResource:
    """Test StudyResource data class"""
    
    def test_create_basic_resource(self):
        """Test creating a basic study resource"""
        resource = StudyResource(
            title="Test Resource",
            url="https://example.com",
            type=ResourceType.OFFICIAL_GUIDE
        )
        
        assert resource.title == "Test Resource"
        assert resource.url == "https://example.com"
        assert resource.type == ResourceType.OFFICIAL_GUIDE
    
    def test_create_full_resource(self):
        """Test creating a resource with all fields"""
        resource = StudyResource(
            title="Complete Guide",
            url="https://example.com/guide",
            type=ResourceType.VIDEO_COURSE,
            description="Comprehensive video course",
            rating=4.5,
            review_count=200,
            duration_hours=40,
            difficulty_level="Intermediate",
            last_updated="2023-01-01"
        )
        
        assert resource.title == "Complete Guide"
        assert resource.rating == 4.5
        assert resource.duration_hours == 40
        assert resource.difficulty_level == "Intermediate"


@pytest.fixture
def sample_resources():
    """Fixture providing sample study resources"""
    return [
        StudyResource(
            title="Official CISSP Guide",
            url="https://isc2.org/cissp-guide",
            type=ResourceType.OFFICIAL_GUIDE,
            rating=4.8,
            review_count=500
        ),
        StudyResource(
            title="CISSP Video Course",
            url="https://training.com/cissp",
            type=ResourceType.VIDEO_COURSE,
            rating=4.2,
            review_count=150,
            duration_hours=30
        ),
        StudyResource(
            title="Practice Exam",
            url="https://practice.com/cissp",
            type=ResourceType.PRACTICE_EXAM,
            rating=4.0,
            review_count=75
        )
    ]


def test_resource_filtering_by_type(sample_resources):
    """Test filtering resources by type"""
    video_courses = [r for r in sample_resources if r.type == ResourceType.VIDEO_COURSE]
    assert len(video_courses) == 1
    assert video_courses[0].title == "CISSP Video Course"


def test_resource_sorting_by_rating(sample_resources):
    """Test sorting resources by rating"""
    sorted_resources = sorted(sample_resources, key=lambda r: r.rating, reverse=True)
    assert sorted_resources[0].rating == 4.8
    assert sorted_resources[-1].rating == 4.0
