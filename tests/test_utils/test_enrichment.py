"""Tests for certification data enrichment utilities"""
import pytest
from unittest.mock import patch, MagicMock, mock_open
import requests
from bs4 import BeautifulSoup
from utils.enrichment import (
    scrape_certification_info,
    enrich_certification_data,
    extract_exam_details,
    get_market_demand,
    analyze_job_requirements,
    update_certification_trends,
    CertificationEnricher
)


class TestScrapeCertificationInfo:
    """Test web scraping functionality for certification info"""
    
    @patch('utils.enrichment.requests.get')
    def test_scrape_valid_url(self, mock_get):
        """Test scraping from a valid URL"""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.content = b"""
        <html>
            <body>
                <h1>CISSP Certification</h1>
                <p>Cost: $749</p>
                <p>Duration: 6 hours</p>
                <div class="description">Security management certification</div>
            </body>
        </html>
        """
        mock_get.return_value = mock_response
        
        result = scrape_certification_info("https://example.com/cissp")
        assert result is not None
        assert isinstance(result, dict)
    
    @patch('utils.enrichment.requests.get')
    def test_scrape_invalid_url(self, mock_get):
        """Test scraping from invalid URL"""
        mock_response = MagicMock()
        mock_response.status_code = 404
        mock_get.return_value = mock_response
        
        result = scrape_certification_info("https://example.com/404")
        assert result is None
    
    @patch('utils.enrichment.requests.get')
    def test_scrape_with_timeout(self, mock_get):
        """Test scraping with timeout"""
        mock_get.side_effect = requests.Timeout()
        
        result = scrape_certification_info("https://slow-site.com")
        assert result is None
    
    @patch('utils.enrichment.requests.get')
    def test_scrape_with_connection_error(self, mock_get):
        """Test scraping with connection error"""
        mock_get.side_effect = requests.ConnectionError()
        
        result = scrape_certification_info("https://unreachable.com")
        assert result is None


class TestEnrichCertificationData:
    """Test certification data enrichment"""
    
    @patch('utils.enrichment.scrape_certification_info')
    @patch('utils.enrichment.get_market_demand')
    def test_enrich_basic_certification(self, mock_market_demand, mock_scrape):
        """Test enriching basic certification data"""
        # Mock scraped data
        mock_scrape.return_value = {
            'cost': 749,
            'duration_hours': 6,
            'passing_score': 700,
            'description': 'Advanced security certification'
        }
        
        # Mock market demand data
        mock_market_demand.return_value = {
            'demand_score': 8.5,
            'avg_salary': 120000,
            'job_count': 1500
        }
        
        base_data = {
            'name': 'CISSP',
            'domain': 'Security Management',
            'level': 'Advanced'
        }
        
        enriched = enrich_certification_data(base_data)
        assert enriched['cost'] == 749
        assert enriched['demand_score'] == 8.5
        assert 'description' in enriched
    
    def test_enrich_certification_no_url(self):
        """Test enriching certification without URL"""
        base_data = {
            'name': 'Custom Cert',
            'domain': 'Security',
            'level': 'Intermediate'
        }
        
        enriched = enrich_certification_data(base_data)
        # Should return original data if no URL to scrape
        assert enriched['name'] == 'Custom Cert'
        assert enriched['domain'] == 'Security'
    
    @patch('utils.enrichment.scrape_certification_info')
    def test_enrich_certification_scrape_fails(self, mock_scrape):
        """Test enrichment when scraping fails"""
        mock_scrape.return_value = None
        
        base_data = {
            'name': 'CISSP',
            'url': 'https://example.com/cissp'
        }
        
        enriched = enrich_certification_data(base_data)
        # Should still return the base data
        assert enriched['name'] == 'CISSP'


class TestExtractExamDetails:
    """Test exam details extraction"""
    
    def test_extract_from_html_content(self):
        """Test extracting exam details from HTML"""
        html_content = """
        <div class="exam-info">
            <p>Exam Duration: 6 hours</p>
            <p>Number of Questions: 100-150</p>
            <p>Passing Score: 700</p>
            <p>Exam Fee: $749</p>
        </div>
        """
        
        soup = BeautifulSoup(html_content, 'html.parser')
        details = extract_exam_details(soup)
        
        assert isinstance(details, dict)
        # Should extract numeric values
        assert 'duration_hours' in details or 'questions' in details
    
    def test_extract_from_empty_content(self):
        """Test extracting from empty HTML"""
        soup = BeautifulSoup("<html></html>", 'html.parser')
        details = extract_exam_details(soup)
        
        assert isinstance(details, dict)
        assert len(details) == 0
    
    def test_extract_with_various_formats(self):
        """Test extraction with various number formats"""
        html_content = """
        <div>
            <p>Duration: 3.5 hours</p>
            <p>Questions: 85-100</p>
            <p>Cost: $500 USD</p>
            <p>Score: 70%</p>
        </div>
        """
        
        soup = BeautifulSoup(html_content, 'html.parser')
        details = extract_exam_details(soup)
        
        assert isinstance(details, dict)


class TestGetMarketDemand:
    """Test market demand analysis"""
    
    @patch('utils.enrichment.requests.get')
    def test_get_demand_for_popular_cert(self, mock_get):
        """Test getting market demand for popular certification"""
        # Mock job board API response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'jobs': [
                {'title': 'Security Analyst', 'salary': 80000},
                {'title': 'Security Engineer', 'salary': 100000},
                {'title': 'CISO', 'salary': 150000}
            ],
            'total_count': 1200
        }
        mock_get.return_value = mock_response
        
        demand = get_market_demand('CISSP')
        assert isinstance(demand, dict)
        assert 'job_count' in demand
        assert 'avg_salary' in demand
    
    @patch('utils.enrichment.requests.get')
    def test_get_demand_api_failure(self, mock_get):
        """Test market demand when API fails"""
        mock_get.side_effect = requests.RequestException()
        
        demand = get_market_demand('CISSP')
        # Should return default/empty data
        assert isinstance(demand, dict)
    
    def test_get_demand_unknown_cert(self):
        """Test market demand for unknown certification"""
        demand = get_market_demand('UNKNOWN_CERT_12345')
        assert isinstance(demand, dict)
        # Should handle gracefully


class TestAnalyzeJobRequirements:
    """Test job requirements analysis"""
    
    def test_analyze_job_descriptions(self):
        """Test analyzing job descriptions for certification requirements"""
        job_descriptions = [
            "Security Analyst position requiring CISSP certification and 5 years experience",
            "Looking for CISSP certified professional with cloud security knowledge",
            "Senior role needs CISSP, CISM, or equivalent certification"
        ]
        
        analysis = analyze_job_requirements(job_descriptions, 'CISSP')
        assert isinstance(analysis, dict)
        assert 'mention_frequency' in analysis
        assert analysis['mention_frequency'] > 0
    
    def test_analyze_empty_descriptions(self):
        """Test analyzing empty job descriptions"""
        analysis = analyze_job_requirements([], 'CISSP')
        assert isinstance(analysis, dict)
        assert analysis['mention_frequency'] == 0
    
    def test_analyze_case_insensitive(self):
        """Test that analysis is case insensitive"""
        job_descriptions = [
            "Need cissp certification",
            "CISSP required",
            "Cissp preferred"
        ]
        
        analysis = analyze_job_requirements(job_descriptions, 'CISSP')
        assert analysis['mention_frequency'] == 3


class TestUpdateCertificationTrends:
    """Test certification trends updating"""
    
    @patch('utils.enrichment.get_market_demand')
    @patch('utils.enrichment.analyze_job_requirements')
    def test_update_trends_for_certification(self, mock_analyze, mock_demand):
        """Test updating trends for a certification"""
        mock_demand.return_value = {
            'job_count': 1500,
            'avg_salary': 120000,
            'demand_score': 8.5
        }
        
        mock_analyze.return_value = {
            'mention_frequency': 0.75,
            'required_vs_preferred': 0.6
        }
        
        trends = update_certification_trends('CISSP')
        assert isinstance(trends, dict)
        assert 'demand_score' in trends
        assert 'job_count' in trends
    
    @patch('utils.enrichment.get_market_demand')
    def test_update_trends_api_failure(self, mock_demand):
        """Test updating trends when APIs fail"""
        mock_demand.side_effect = Exception("API Error")
        
        trends = update_certification_trends('CISSP')
        # Should handle errors gracefully
        assert isinstance(trends, dict)


class TestCertificationEnricher:
    """Test CertificationEnricher class"""
    
    def test_enricher_initialization(self):
        """Test initializing the enricher"""
        enricher = CertificationEnricher()
        assert enricher is not None
        assert hasattr(enricher, 'enrich')
    
    @patch('utils.enrichment.scrape_certification_info')
    def test_enricher_enrich_method(self, mock_scrape):
        """Test the enrich method"""
        mock_scrape.return_value = {
            'cost': 500,
            'description': 'Test certification'
        }
        
        enricher = CertificationEnricher()
        cert_data = {
            'name': 'Test Cert',
            'url': 'https://example.com'
        }
        
        enriched = enricher.enrich(cert_data)
        assert enriched['name'] == 'Test Cert'
        assert 'cost' in enriched
    
    def test_enricher_batch_processing(self):
        """Test batch processing multiple certifications"""
        enricher = CertificationEnricher()
        cert_list = [
            {'name': 'Cert1', 'domain': 'Security'},
            {'name': 'Cert2', 'domain': 'Network'},
            {'name': 'Cert3', 'domain': 'Cloud'}
        ]
        
        enriched_list = enricher.enrich_batch(cert_list)
        assert len(enriched_list) == 3
        assert all('name' in cert for cert in enriched_list)


class TestDataValidation:
    """Test data validation in enrichment process"""
    
    def test_validate_enriched_data(self):
        """Test validation of enriched data"""
        from utils.enrichment import validate_enriched_data
        
        valid_data = {
            'name': 'CISSP',
            'cost': 749,
            'duration_hours': 6,
            'passing_score': 700
        }
        
        is_valid = validate_enriched_data(valid_data)
        assert is_valid is True
    
    def test_validate_invalid_data(self):
        """Test validation of invalid enriched data"""
        from utils.enrichment import validate_enriched_data
        
        invalid_data = {
            'name': '',  # Empty name
            'cost': -100,  # Negative cost
            'duration_hours': 'invalid'  # Non-numeric duration
        }
        
        is_valid = validate_enriched_data(invalid_data)
        assert is_valid is False


@pytest.fixture
def sample_certification_data():
    """Fixture providing sample certification data"""
    return {
        'name': 'CISSP',
        'domain': 'Security Management',
        'level': 'Advanced',
        'url': 'https://isc2.org/cissp'
    }


@pytest.fixture
def mock_html_content():
    """Fixture providing mock HTML content"""
    return """
    <html>
        <head><title>CISSP Certification</title></head>
        <body>
            <h1>CISSP - Certified Information Systems Security Professional</h1>
            <div class="exam-details">
                <p>Exam Cost: $749</p>
                <p>Exam Duration: 6 hours</p>
                <p>Number of Questions: 100-150</p>
                <p>Passing Score: 700 out of 1000</p>
            </div>
            <div class="description">
                <p>The CISSP is an advanced certification for security professionals.</p>
            </div>
        </body>
    </html>
    """


def test_integration_enrichment_workflow(sample_certification_data, mock_html_content):
    """Test complete enrichment workflow"""
    with patch('utils.enrichment.requests.get') as mock_get:
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.content = mock_html_content.encode()
        mock_get.return_value = mock_response
        
        enricher = CertificationEnricher()
        enriched = enricher.enrich(sample_certification_data)
        
        # Should contain original data
        assert enriched['name'] == 'CISSP'
        assert enriched['domain'] == 'Security Management'
        
        # Should contain enriched data
        assert isinstance(enriched, dict)
