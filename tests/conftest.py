"""Test configuration and fixtures"""
from models.certification_explorer import CertificationExplorer
from models.learning_path import LearningPath
from models.job import SecurityJob
from models.reports import Report
from models.certification import Certification, Organization
from models.user import User, UserPreference, UserActivity
from database import Base, get_db
from api.app import app
import pytest
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from fastapi.testclient import TestClient
import sys
import os
from pathlib import Path
import logging
import uuid
from unittest.mock import MagicMock, patch

# Set environment variables before importing app
os.environ.setdefault(
    'DATABASE_URL', 'postgresql://postgres:postgres@localhost:5432/certrats_test')
os.environ.setdefault('ENVIRONMENT', 'test')


# Import all models to ensure they're registered with Base

# Add project root to Python path
project_root = str(Path(__file__).parent.parent)
if project_root not in sys.path:
    sys.path.append(project_root)

# Set up logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_test_database_url():
    """Create a unique test database URL"""
    # Generate a unique database name for this test session
    test_db_name = f"certrats_test_{uuid.uuid4().hex[:8]}"

    # Use the working PostgreSQL connection
    base_url = "postgresql://postgres:postgres@localhost:5432"

    return f"{base_url}/{test_db_name}", test_db_name


@pytest.fixture(scope="session")
def db_engine():
    """Create a test database engine with a unique PostgreSQL database"""
    test_db_url, test_db_name = create_test_database_url()

    # First, connect to the default postgres database to create our test database
    admin_url = "postgresql://postgres:postgres@localhost:5432/postgres"
    admin_engine = create_engine(admin_url)

    try:
        # Create the test database
        with admin_engine.connect() as conn:
            # Use autocommit mode for database creation
            conn.execute(text("COMMIT"))
            conn.execute(text(f"CREATE DATABASE {test_db_name}"))
        logger.info(f"Created test database: {test_db_name}")

        # Now create the engine for our test database
        engine = create_engine(
            test_db_url,
            pool_pre_ping=True,
            echo=False  # Set to True for SQL debugging
        )

        # Create all tables
        Base.metadata.create_all(engine)
        logger.info("Created all tables in test database")

        yield engine

    finally:
        # Cleanup: drop the test database
        try:
            engine.dispose()
            with admin_engine.connect() as conn:
                # Terminate any remaining connections to the test database
                conn.execute(text("COMMIT"))
                conn.execute(text(f"""
                    SELECT pg_terminate_backend(pid)
                    FROM pg_stat_activity
                    WHERE datname = '{test_db_name}' AND pid <> pg_backend_pid()
                """))
                conn.execute(text(f"DROP DATABASE IF EXISTS {test_db_name}"))
            logger.info(f"Dropped test database: {test_db_name}")
        except Exception as e:
            logger.warning(
                f"Failed to cleanup test database {test_db_name}: {e}")
        finally:
            admin_engine.dispose()


@pytest.fixture(scope="function")
def db_session(db_engine):
    """Create a new database session for a test with transaction rollback"""
    connection = db_engine.connect()
    transaction = connection.begin()
    session = sessionmaker(bind=connection)()

    yield session

    # Cleanup: rollback transaction and close connections
    session.close()
    transaction.rollback()
    connection.close()


@pytest.fixture(scope="function")
def test_client(db_session):
    """Create a test client with database override"""
    def override_get_db():
        try:
            yield db_session
        finally:
            pass

    app.dependency_overrides[get_db] = override_get_db
    client = TestClient(app)
    yield client
    app.dependency_overrides.clear()


@pytest.fixture(scope="function")
def clean_db_session(db_engine):
    """Create a clean database session that commits changes (for integration tests)"""
    session = sessionmaker(bind=db_engine)()

    yield session

    # Cleanup: remove all data but keep schema
    try:
        # Delete in reverse order of dependencies to avoid foreign key constraints
        session.execute(text("TRUNCATE TABLE user_activities CASCADE"))
        session.execute(text("TRUNCATE TABLE user_preferences CASCADE"))
        session.execute(text("TRUNCATE TABLE reports CASCADE"))
        session.execute(text("TRUNCATE TABLE certifications CASCADE"))
        session.execute(text("TRUNCATE TABLE organizations CASCADE"))
        session.execute(text("TRUNCATE TABLE users CASCADE"))
        session.commit()
    except Exception as e:
        logger.warning(f"Failed to clean database: {e}")
        session.rollback()
    finally:
        session.close()
